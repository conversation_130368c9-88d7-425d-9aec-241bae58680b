module.exports = function saveAsyncStack(instance, lines) {
  if (instance.client.config.asyncStackTraces) {
    // a hack to get a callstack into the client code despite this
    // node.js bug https://github.com/nodejs/node/issues/11865

    // Save error here but not error trace
    // reading trace with '--enable-source-maps' flag on node can be very costly

    instance._asyncStack = {
      error: new Error(),
      lines,
    };
  }
};
