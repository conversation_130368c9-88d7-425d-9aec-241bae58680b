// All properties we can use to start a query chain
// from the `knex` object, e.g. `knex.select('*').from(...`
module.exports = [
  'with',
  'withRecursive',
  'withMaterialized',
  'withNotMaterialized',
  'select',
  'as',
  'columns',
  'column',
  'from',
  'fromJS',
  'fromRaw',
  'into',
  'withSchema',
  'table',
  'distinct',
  'join',
  'joinRaw',
  'innerJoin',
  'leftJoin',
  'leftOuterJoin',
  'rightJoin',
  'rightOuterJoin',
  'outerJoin',
  'fullOuterJoin',
  'crossJoin',
  'where',
  'andWhere',
  'orWhere',
  'whereNot',
  'orWhereNot',
  'whereLike',
  'andWhereLike',
  'orWhereLike',
  'whereILike',
  'andWhereILike',
  'orWhereILike',
  'whereRaw',
  'whereWrapped',
  'havingWrapped',
  'orWhereRaw',
  'whereExists',
  'orWhereExists',
  'whereNotExists',
  'orWhereNotExists',
  'whereIn',
  'orWhereIn',
  'whereNotIn',
  'orWhereNotIn',
  'whereNull',
  'orWhereNull',
  'whereNotNull',
  'orWhereNotNull',
  'whereBetween',
  'whereNotBetween',
  'andWhereBetween',
  'andWhereNotBetween',
  'orWhereBetween',
  'orWhereNotBetween',
  'groupBy',
  'groupByRaw',
  'orderBy',
  'orderByRaw',
  'union',
  'unionAll',
  'intersect',
  'except',
  'having',
  'havingRaw',
  'orHaving',
  'orHavingRaw',
  'offset',
  'limit',
  'count',
  'countDistinct',
  'min',
  'max',
  'sum',
  'sumDistinct',
  'avg',
  'avgDistinct',
  'increment',
  'decrement',
  'first',
  'debug',
  'pluck',
  'clearSelect',
  'clearWhere',
  'clearGroup',
  'clearOrder',
  'clearHaving',
  'insert',
  'update',
  'returning',
  'del',
  'delete',
  'truncate',
  'transacting',
  'connection',

  // JSON methods

  // Json manipulation functions
  'jsonExtract',
  'jsonSet',
  'jsonInsert',
  'jsonRemove',

  // Wheres Json
  'whereJsonObject',
  'orWhereJsonObject',
  'andWhereJsonObject',
  'whereNotJsonObject',
  'orWhereNotJsonObject',
  'andWhereNotJsonObject',

  'whereJsonPath',
  'orWhereJsonPath',
  'andWhereJsonPath',

  'whereJsonSupersetOf',
  'orWhereJsonSupersetOf',
  'andWhereJsonSupersetOf',
  'whereJsonNotSupersetOf',
  'orWhereJsonNotSupersetOf',
  'andWhereJsonNotSupersetOf',

  'whereJsonSubsetOf',
  'orWhereJsonSubsetOf',
  'andWhereJsonSubsetOf',
  'whereJsonNotSubsetOf',
  'orWhereJsonNotSubsetOf',
  'andWhereJsonNotSubsetOf',
];
