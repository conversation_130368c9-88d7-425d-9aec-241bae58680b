'use strict';

const assert = require('assert');
const processNested = require('../lib/processNested');

describe('processNested: Test Convert Flatten object to Nested object', function() {
  it('With no nested data', () => {
    const data = {
        'firstname': '<PERSON>',
        'lastname': '<PERSON><PERSON>',
        'age': 22
      },
      excerpt = { firstname: '<PERSON>', lastname: '<PERSON><PERSON>', age: 22 },
      processed = processNested(data);

    assert.deepEqual(processed, excerpt);
  });

  it('With nested data', () => {
    const data = {
        'firstname': '<PERSON>',
        'lastname': '<PERSON><PERSON>',
        'age': 22,
        'hobbies[0]': 'Cinema',
        'hobbies[1]': '<PERSON><PERSON>',
        'address[line]': '78  Lynch Street',
        'address[city]': 'Milwaukee',
        'friends[0][name]': '<PERSON>',
        'friends[0][lastname]': 'Do<PERSON>',
        'friends[1][name]': '<PERSON>',
        'friends[1][lastname]': '<PERSON><PERSON>'
      },
      excerpt = {
        firstname: '<PERSON>',
        lastname: '<PERSON><PERSON>',
        age: 22,
        hobbies: [ 'Cinema', 'Bike' ],
        address: { line: '78  Lynch Street', city: 'Milwaukee' },
        friends: [
          { name: '<PERSON>', lastname: 'Doe' },
          { name: '<PERSON>', lastname: 'Doe' }
        ]
      },
      processed = processNested(data);

    assert.deepEqual(processed, excerpt);
  });

  it('Do not allow prototype pollution', () => {
    const pollutionOb1 = JSON.parse(`{"__proto__.POLLUTED1": "FOOBAR"}`);
    const pollutionOb2 = JSON.parse(`{"constructor.prototype.POLLUTED2": "FOOBAR"}`);

    processNested(pollutionOb1);
    processNested(pollutionOb2);

    assert.equal(global.POLLUTED1, undefined);
    assert.equal(global.POLLUTED2, undefined);
  });
});
