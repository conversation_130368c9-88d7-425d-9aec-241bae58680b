{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"默认管理员账户创建成功: admin/admin123456","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:35:35"}
{"ip":"::1","level":"info","message":"GET /health","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:02","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"GET /","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:05","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:05","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/config/app","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:16","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:24","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:36:24","username":"admin"}
{"level":"info","message":"收到SIGINT信号，开始优雅关闭...","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:40:23"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:05"}
{"ip":"::1","level":"info","message":"GET /v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:41:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:18","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:18","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:24","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:25","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:25","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:26","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:28","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:28","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:29","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:283:40\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:29","url":"/v1/admin/careers?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:30","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/orders","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:31","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:355:39\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:31","url":"/v1/admin/orders?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/config","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/status","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:50","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:51","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:52","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:56","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:283:40\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:57","url":"/v1/admin/careers?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:57","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:58","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:59","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:44:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:44:59","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:283:40\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:00","url":"/v1/admin/careers?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:02","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:02","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:03","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/config","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:05","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/status","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:05","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/orders","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:08","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:355:39\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:08","url":"/v1/admin/orders?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:09","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:12","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:45:12","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/config","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/status","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:45:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:00","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:01","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:01","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:01","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:01","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:02","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:02","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:283:40\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:02","url":"/v1/admin/careers?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:03","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:46:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:46:03","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:31","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:31","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:32","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:33","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:33","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:37","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:37","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:54","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:175:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:56","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:211:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:56","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:283:40\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:58","url":"/v1/admin/careers?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:247:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:58","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/simulations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:48:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:319:44\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:48:59","url":"/v1/admin/simulations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/orders","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:49:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:355:39\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:49:00","url":"/v1/admin/orders?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:35","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:35","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:35","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:44","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:44","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:44","userAgent":"curl/7.61.1"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:50:44","url":"/v1/admin/users?page=1&size=20","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:50:55","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:50:55","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:18"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:36"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:51:52"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:09"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:27"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:52:57"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:53:59","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:54:00","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:54:00","userAgent":"curl/7.61.1"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:139:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:54:00","url":"/v1/admin/users?page=1&size=20","userAgent":"curl/7.61.1"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:47"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:56:48"}
{"ip":"::1","level":"info","message":"GET /v1/admin/status","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:40","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:57:40","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:41","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:406:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:57:41","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:746:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:57:45","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:57:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:782:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:57:46","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:38","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:406:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:39","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:746:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:45","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:782:48\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:46","url":"/v1/admin/recommendations?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:406:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:47","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:47","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:746:45\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:48","url":"/v1/admin/universities?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:58:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:406:41\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:58:50","url":"/v1/admin/profiles?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:18","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:18","username":"admin"}
{"ip":"::1","level":"info","message":"POST /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:18","userAgent":"curl/7.61.1"}
{"adminId":"1","level":"info","message":"管理员添加用户","phone":"13800138000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:18","userId":"6eab7490-02b3-4d83-8ddc-2df649a6ac07"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:59:24","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:25","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:26","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:26","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:26","userAgent":"curl/7.61.1"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:59:26","url":"/v1/admin/users?page=1&size=20","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Incorrect arguments to mysqld_stmt_execute","ip":"::1","level":"error","message":"API错误:","method":"GET","service":"zhiyuan-ai-api","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromiseConnection.execute (/root/zengleilei/workspace/ai_baokao/node_modules/mysql2/lib/promise/connection.js:47:22)\n    at /root/zengleilei/workspace/ai_baokao/src/routes/admin.ts:140:38\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-06-09 13:59:30","url":"/v1/admin/users?page=1&size=20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 13:59:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:26"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:00:56"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:16"}
{"level":"info","message":"MySQL数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"数据库表创建完成","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"数据库连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"Redis连接成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"服务器启动成功，端口: 3000","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"管理后台: http://localhost:3000/admin","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"level":"info","message":"API文档: http://localhost:3000/v1","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:31"}
{"ip":"::1","level":"info","message":"POST /v1/admin/login","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:33","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"管理员登录成功","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:33","username":"admin"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:01:33","userAgent":"curl/7.61.1"}
{"ip":"::1","level":"info","message":"GET /v1/admin/dashboard","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:42","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/careers","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/recommendations","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/universities","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/profiles","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"info","message":"GET /v1/admin/users","service":"zhiyuan-ai-api","timestamp":"2025-06-09 14:02:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
