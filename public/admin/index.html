<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>志愿AI规划师 - 数据库管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            border-radius: 0.5rem;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
            border: none;
        }
        .table-container {
            background: white;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            max-width: 400px;
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.1);
            border: 1px solid rgba(255,255,255,0.2);
        }
        .stats-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container d-flex align-items-center justify-content-center">
        <div class="login-card card p-4">
            <div class="text-center mb-4">
                <h2 class="text-white">管理后台登录</h2>
                <p class="text-white-50">志愿AI规划师数据库管理系统</p>
            </div>
            <form id="loginForm">
                <div class="mb-3">
                    <label for="username" class="form-label text-white">用户名</label>
                    <input type="text" class="form-control" id="username" required>
                </div>
                <div class="mb-3">
                    <label for="password" class="form-label text-white">密码</label>
                    <input type="password" class="form-control" id="password" required>
                </div>
                <button type="submit" class="btn btn-light w-100">登录</button>
            </form>
        </div>
    </div>

    <!-- 主页面 -->
    <div id="mainPage" class="d-none">
        <div class="row g-0">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 sidebar p-3">
                <h4 class="text-white mb-4">
                    <i class="bi bi-database"></i> 管理后台
                </h4>
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" data-section="dashboard">
                        <i class="bi bi-speedometer2"></i> 控制台
                    </a>
                    <a class="nav-link" href="#" data-section="users">
                        <i class="bi bi-people"></i> 用户管理
                    </a>
                    <a class="nav-link" href="#" data-section="profiles">
                        <i class="bi bi-person-badge"></i> 考生档案
                    </a>
                    <a class="nav-link" href="#" data-section="universities">
                        <i class="bi bi-building"></i> 院校专业
                    </a>
                    <a class="nav-link" href="#" data-section="recommendations">
                        <i class="bi bi-lightbulb"></i> 推荐记录
                    </a>
                    <a class="nav-link" href="#" data-section="careers">
                        <i class="bi bi-briefcase"></i> 职业数据
                    </a>
                    <a class="nav-link" href="#" data-section="simulations">
                        <i class="bi bi-graph-up"></i> 模拟记录
                    </a>
                    <a class="nav-link" href="#" data-section="orders">
                        <i class="bi bi-credit-card"></i> 订单管理
                    </a>
                    <a class="nav-link" href="#" data-section="system">
                        <i class="bi bi-gear"></i> 系统设置
                    </a>
                </nav>
                <div class="mt-auto pt-3">
                    <button class="btn btn-outline-light btn-sm w-100" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </button>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="col-md-9 col-lg-10 p-4">
                <!-- 控制台 -->
                <div id="dashboard" class="content-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="mb-0">控制台</h2>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshCurrentData()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="row g-3 mb-4">
                        <div class="col-md-3">
                            <div class="card stats-card text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">总用户数</h6>
                                            <h3 id="totalUsers">-</h3>
                                        </div>
                                        <i class="bi bi-people fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stats-card text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">考生档案</h6>
                                            <h3 id="totalProfiles">-</h3>
                                        </div>
                                        <i class="bi bi-person-badge fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stats-card text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">推荐记录</h6>
                                            <h3 id="totalRecommendations">-</h3>
                                        </div>
                                        <i class="bi bi-lightbulb fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stats-card text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="card-title">总订单数</h6>
                                            <h3 id="totalOrders">-</h3>
                                        </div>
                                        <i class="bi bi-credit-card fs-1 opacity-75"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最近活动 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">最近活动</h5>
                        </div>
                        <div class="card-body">
                            <div id="recentActivities">加载中...</div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理 -->
                <div id="users" class="content-section d-none">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>用户管理</h2>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="refreshCurrentData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="bi bi-plus"></i> 添加用户
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table table-hover mb-0" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>手机号</th>
                                    <th>昵称</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center mt-3" id="usersPagination"></ul>
                    </nav>
                </div>

                <!-- 其他内容区域将通过JavaScript动态加载 -->
                <div id="profiles" class="content-section d-none">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2>考生档案管理</h2>
                        <div>
                            <button class="btn btn-outline-secondary btn-sm me-2" onclick="refreshCurrentData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button class="btn btn-primary" onclick="showAddProfileModal()">
                                <i class="bi bi-plus"></i> 添加档案
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table table-hover" id="profilesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>分数</th>
                                    <th>省份</th>
                                    <th>科目组合</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="universities" class="content-section d-none">
                    <h2>院校专业管理</h2>
                    <div class="table-container">
                        <table class="table table-hover" id="universitiesTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>院校名称</th>
                                    <th>专业名称</th>
                                    <th>院校层次</th>
                                    <th>省份</th>
                                    <th>城市</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="recommendations" class="content-section d-none">
                    <h2>推荐记录管理</h2>
                    <div class="table-container">
                        <table class="table table-hover" id="recommendationsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>档案ID</th>
                                    <th>推荐类型</th>
                                    <th>生成时间</th>
                                    <th>是否付费</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="careers" class="content-section d-none">
                    <h2>职业数据管理</h2>
                    <div class="table-container">
                        <table class="table table-hover" id="careersTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>职业名称</th>
                                    <th>职业类别</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="simulations" class="content-section d-none">
                    <h2>模拟记录管理</h2>
                    <div class="table-container">
                        <table class="table table-hover" id="simulationsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>档案ID</th>
                                    <th>选择院校</th>
                                    <th>选择专业</th>
                                    <th>目标职业</th>
                                    <th>生成时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="orders" class="content-section d-none">
                    <h2>订单管理</h2>
                    <div class="table-container">
                        <table class="table table-hover" id="ordersTable">
                            <thead class="table-light">
                                <tr>
                                    <th>订单ID</th>
                                    <th>用户ID</th>
                                    <th>产品类型</th>
                                    <th>金额</th>
                                    <th>支付状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>

                <div id="system" class="content-section d-none">
                    <h2>系统设置</h2>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>基础配置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="systemConfigForm">
                                        <div class="mb-3">
                                            <label class="form-label">当前年份</label>
                                            <input type="number" class="form-control" id="currentYear" value="2025">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">详细分析价格</label>
                                            <input type="number" class="form-control" id="detailedAnalysisPrice" value="19.9" step="0.01">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模拟报告价格</label>
                                            <input type="number" class="form-control" id="simulationReportPrice" value="199" step="0.01">
                                        </div>
                                        <button type="submit" class="btn btn-primary">保存设置</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>系统状态</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-2">
                                        <strong>数据库状态:</strong> 
                                        <span class="badge bg-success">正常</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>Redis状态:</strong> 
                                        <span class="badge bg-success">正常</span>
                                    </div>
                                    <div class="mb-2">
                                        <strong>API状态:</strong> 
                                        <span class="badge bg-success">正常</span>
                                    </div>
                                    <button class="btn btn-outline-primary btn-sm" onclick="checkSystemStatus()">刷新状态</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 用户管理模态框 -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="userModalTitle">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="userId" name="userId">
                        <div class="mb-3">
                            <label for="userPhone" class="form-label">手机号 *</label>
                            <input type="tel" class="form-control" id="userPhone" name="phone" required>
                        </div>
                        <div class="mb-3">
                            <label for="userNickname" class="form-label">昵称</label>
                            <input type="text" class="form-control" id="userNickname" name="nickname">
                        </div>
                        <div class="mb-3">
                            <label for="userStatus" class="form-label">状态</label>
                            <select class="form-select" id="userStatus" name="status">
                                <option value="active">正常</option>
                                <option value="inactive">未激活</option>
                                <option value="banned">已封禁</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 考生档案管理模态框 -->
    <div class="modal fade" id="profileModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalTitle">添加考生档案</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="profileForm">
                        <input type="hidden" id="profileId" name="profileId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileUserId" class="form-label">用户ID *</label>
                                    <input type="text" class="form-control" id="profileUserId" name="user_id" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileName" class="form-label">姓名 *</label>
                                    <input type="text" class="form-control" id="profileName" name="name" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileScore" class="form-label">分数 *</label>
                                    <input type="number" class="form-control" id="profileScore" name="score" min="0" max="750" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileProvince" class="form-label">省份 *</label>
                                    <select class="form-select" id="profileProvince" name="province" required>
                                        <option value="">请选择省份</option>
                                        <option value="beijing">北京</option>
                                        <option value="shanghai">上海</option>
                                        <option value="guangdong">广东</option>
                                        <option value="jiangsu">江苏</option>
                                        <option value="zhejiang">浙江</option>
                                        <option value="hubei">湖北</option>
                                        <option value="hunan">湖南</option>
                                        <option value="sichuan">四川</option>
                                        <option value="henan">河南</option>
                                        <option value="shandong">山东</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileSubjectCombination" class="form-label">科目组合 *</label>
                                    <select class="form-select" id="profileSubjectCombination" name="subject_combination" required>
                                        <option value="">请选择科目组合</option>
                                        <option value="physics_chemistry_biology">物理+化学+生物</option>
                                        <option value="physics_chemistry_geography">物理+化学+地理</option>
                                        <option value="physics_biology_geography">物理+生物+地理</option>
                                        <option value="history_politics_geography">历史+政治+地理</option>
                                        <option value="history_politics_chemistry">历史+政治+化学</option>
                                        <option value="history_geography_chemistry">历史+地理+化学</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="profileExamType" class="form-label">考试类型 *</label>
                                    <select class="form-select" id="profileExamType" name="exam_type" required>
                                        <option value="">请选择考试类型</option>
                                        <option value="new_gaokao">新高考</option>
                                        <option value="traditional">传统高考</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="profileFamilyEconomicStatus" class="form-label">家庭经济状况</label>
                            <select class="form-select" id="profileFamilyEconomicStatus" name="family_economic_status">
                                <option value="">请选择</option>
                                <option value="low_income">低收入</option>
                                <option value="middle_class">中等收入</option>
                                <option value="high_income">高收入</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveProfile()">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p id="deleteConfirmText">确定要删除这条记录吗？此操作不可恢复。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="admin.js"></script>
</body>
</html> 