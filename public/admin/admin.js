// 全局变量
let authToken = localStorage.getItem('admin_token');
let currentPage = 1;
let currentSection = 'dashboard';

// API基础URL
const API_BASE = '/v1';

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    if (authToken) {
        showMainPage();
        loadDashboard();
    } else {
        showLoginPage();
    }

    // 绑定登录表单事件
    document.getElementById('loginForm').addEventListener('submit', handleLogin);

    // 绑定导航事件
    document.querySelectorAll('[data-section]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });

    // 绑定系统配置表单事件
    const systemConfigForm = document.getElementById('systemConfigForm');
    if (systemConfigForm) {
        systemConfigForm.addEventListener('submit', handleSystemConfigSave);
    }
});

// 显示登录页面
function showLoginPage() {
    document.getElementById('loginPage').classList.remove('d-none');
    document.getElementById('mainPage').classList.add('d-none');
}

// 显示主页面
function showMainPage() {
    document.getElementById('loginPage').classList.add('d-none');
    document.getElementById('mainPage').classList.remove('d-none');
}

// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    try {
        const response = await fetch(`${API_BASE}/admin/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (data.code === 200) {
            authToken = data.data.access_token;
            localStorage.setItem('admin_token', authToken);
            showMainPage();
            loadDashboard();
            showAlert('登录成功', 'success');
        } else {
            showAlert(data.message || '登录失败', 'danger');
        }
    } catch (error) {
        console.error('登录错误:', error);
        showAlert('网络错误，请重试', 'danger');
    }
}

// 退出登录
function logout() {
    localStorage.removeItem('admin_token');
    authToken = null;
    showLoginPage();
    showAlert('已退出登录', 'info');
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    try {
        const response = await fetch(`${API_BASE}${url}`, finalOptions);
        const data = await response.json();

        if (response.status === 401) {
            logout();
            return null;
        }

        return data;
    } catch (error) {
        console.error('API请求错误:', error);
        showAlert('网络错误，请重试', 'danger');
        return null;
    }
}

// 显示指定区域
function showSection(section) {
    // 隐藏所有内容区域
    document.querySelectorAll('.content-section').forEach(el => {
        el.classList.add('d-none');
    });

    // 移除所有导航活动状态
    document.querySelectorAll('.nav-link').forEach(el => {
        el.classList.remove('active');
    });

    // 显示指定区域
    document.getElementById(section).classList.remove('d-none');

    // 设置导航活动状态
    document.querySelector(`[data-section="${section}"]`).classList.add('active');

    currentSection = section;

    // 加载对应数据
    switch(section) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'profiles':
            loadProfiles();
            break;
        case 'universities':
            loadUniversities();
            break;
        case 'recommendations':
            loadRecommendations();
            break;
        case 'careers':
            loadCareers();
            break;
        case 'simulations':
            loadSimulations();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'system':
            loadSystemConfig();
            break;
    }
}

// 加载控制台数据
async function loadDashboard() {
    try {
        // 显示加载状态
        document.getElementById('totalUsers').textContent = '...';
        document.getElementById('totalProfiles').textContent = '...';
        document.getElementById('totalRecommendations').textContent = '...';
        document.getElementById('totalOrders').textContent = '...';
        document.getElementById('recentActivities').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';

        const data = await apiRequest('/admin/dashboard');
        if (data && data.code === 200) {
            const stats = data.data.stats;
            document.getElementById('totalUsers').textContent = stats.users || 0;
            document.getElementById('totalProfiles').textContent = stats.profiles || 0;
            document.getElementById('totalRecommendations').textContent = stats.recommendations || 0;
            document.getElementById('totalOrders').textContent = stats.orders || 0;

            // 显示最近活动
            const activities = data.data.activities || [];
            const activitiesHtml = activities.length > 0
                ? activities.map(activity => `
                    <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                        <div>
                            <strong>${activity.action}</strong><br>
                            <small class="text-muted">${activity.description}</small>
                        </div>
                        <small class="text-muted">${formatDate(activity.created_at)}</small>
                    </div>
                `).join('')
                : '<p class="text-muted">暂无活动记录</p>';

            document.getElementById('recentActivities').innerHTML = activitiesHtml;
        } else {
            // 错误处理
            document.getElementById('totalUsers').textContent = '-';
            document.getElementById('totalProfiles').textContent = '-';
            document.getElementById('totalRecommendations').textContent = '-';
            document.getElementById('totalOrders').textContent = '-';
            document.getElementById('recentActivities').innerHTML = '<p class="text-danger">加载失败</p>';
            showAlert('加载控制台数据失败', 'danger');
        }
    } catch (error) {
        console.error('加载控制台数据错误:', error);
        document.getElementById('recentActivities').innerHTML = '<p class="text-danger">加载失败</p>';
        showAlert('加载控制台数据失败', 'danger');
    }
}

// 加载用户列表
async function loadUsers(page = 1) {
    try {
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border" role="status"></div></td></tr>';

        const data = await apiRequest(`/admin/users?page=${page}&size=20`);
        if (data && data.code === 200) {
            const users = data.data.users || [];
            const pagination = data.data.pagination;

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无用户数据</td></tr>';
            } else {
                tbody.innerHTML = users.map(user => `
                    <tr>
                        <td>${user.id}</td>
                        <td>${user.phone}</td>
                        <td>${user.nickname || '-'}</td>
                        <td>${formatDate(user.register_time)}</td>
                        <td>${user.last_login_time ? formatDate(user.last_login_time) : '-'}</td>
                        <td>
                            <span class="badge ${getStatusBadgeClass(user.status)}">${getStatusText(user.status)}</span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewUser('${user.id}')">查看</button>
                            <button class="btn btn-sm btn-outline-warning" onclick="editUser('${user.id}')">编辑</button>
                        </td>
                    </tr>
                `).join('');
            }

            // 更新分页
            updatePagination('usersPagination', pagination, 'loadUsers');
        } else {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
            showAlert('加载用户列表失败', 'danger');
        }
    } catch (error) {
        console.error('加载用户列表错误:', error);
        const tbody = document.querySelector('#usersTable tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-danger">加载失败</td></tr>';
        showAlert('加载用户列表失败', 'danger');
    }
}

// 加载考生档案
async function loadProfiles(page = 1) {
    const data = await apiRequest(`/admin/profiles?page=${page}&size=20`);
    if (data && data.code === 200) {
        const profiles = data.data.profiles || [];

        const tbody = document.querySelector('#profilesTable tbody');
        tbody.innerHTML = profiles.map(profile => `
            <tr>
                <td>${profile.id}</td>
                <td>${profile.name}</td>
                <td>${profile.score}</td>
                <td>${profile.province}</td>
                <td>${profile.subject_combination}</td>
                <td>${formatDate(profile.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewProfile('${profile.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载院校专业
async function loadUniversities(page = 1) {
    const data = await apiRequest(`/admin/universities?page=${page}&size=20`);
    if (data && data.code === 200) {
        const universities = data.data.universities || [];

        const tbody = document.querySelector('#universitiesTable tbody');
        tbody.innerHTML = universities.map(uni => `
            <tr>
                <td>${uni.id}</td>
                <td>${uni.university_name}</td>
                <td>${uni.major_name}</td>
                <td>
                    <span class="badge ${getTierBadgeClass(uni.university_tier)}">${uni.university_tier}</span>
                </td>
                <td>${uni.province}</td>
                <td>${uni.city}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewUniversity('${uni.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载推荐记录
async function loadRecommendations(page = 1) {
    const data = await apiRequest(`/admin/recommendations?page=${page}&size=20`);
    if (data && data.code === 200) {
        const recommendations = data.data.recommendations || [];

        const tbody = document.querySelector('#recommendationsTable tbody');
        tbody.innerHTML = recommendations.map(rec => `
            <tr>
                <td>${rec.id}</td>
                <td>${rec.profile_id}</td>
                <td>${rec.recommendation_type}</td>
                <td>${formatDate(rec.generated_at)}</td>
                <td>
                    <span class="badge ${rec.is_paid ? 'bg-success' : 'bg-secondary'}">${rec.is_paid ? '已付费' : '未付费'}</span>
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewRecommendation('${rec.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载职业数据
async function loadCareers(page = 1) {
    const data = await apiRequest(`/admin/careers?page=${page}&size=20`);
    if (data && data.code === 200) {
        const careers = data.data.careers || [];

        const tbody = document.querySelector('#careersTable tbody');
        tbody.innerHTML = careers.map(career => `
            <tr>
                <td>${career.id}</td>
                <td>${career.career_name}</td>
                <td>${career.career_category}</td>
                <td>${formatDate(career.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewCareer('${career.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载模拟记录
async function loadSimulations(page = 1) {
    const data = await apiRequest(`/admin/simulations?page=${page}&size=20`);
    if (data && data.code === 200) {
        const simulations = data.data.simulations || [];

        const tbody = document.querySelector('#simulationsTable tbody');
        tbody.innerHTML = simulations.map(sim => `
            <tr>
                <td>${sim.id}</td>
                <td>${sim.profile_id}</td>
                <td>${sim.selected_university}</td>
                <td>${sim.selected_major}</td>
                <td>${sim.target_career}</td>
                <td>${formatDate(sim.generated_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewSimulation('${sim.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载订单列表
async function loadOrders(page = 1) {
    const data = await apiRequest(`/admin/orders?page=${page}&size=20`);
    if (data && data.code === 200) {
        const orders = data.data.orders || [];

        const tbody = document.querySelector('#ordersTable tbody');
        tbody.innerHTML = orders.map(order => `
            <tr>
                <td>${order.id}</td>
                <td>${order.user_id}</td>
                <td>${order.product_type}</td>
                <td>¥${order.amount}</td>
                <td>
                    <span class="badge ${getPaymentStatusBadgeClass(order.payment_status)}">${getPaymentStatusText(order.payment_status)}</span>
                </td>
                <td>${formatDate(order.created_at)}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewOrder('${order.id}')">查看</button>
                </td>
            </tr>
        `).join('');
    }
}

// 加载系统配置
async function loadSystemConfig() {
    const data = await apiRequest('/admin/config');
    if (data && data.code === 200) {
        const config = data.data;
        document.getElementById('currentYear').value = config.current_year;
        document.getElementById('detailedAnalysisPrice').value = config.pricing.detailed_analysis;
        document.getElementById('simulationReportPrice').value = config.pricing.simulation_report;
    }

    // 检查系统状态
    checkSystemStatus();
}

// 检查系统状态
async function checkSystemStatus() {
    const data = await apiRequest('/admin/status');
    if (data && data.code === 200) {
        const status = data.data;

        // 更新状态显示
        const statusElements = {
            database: document.querySelector('.card-body .mb-2:nth-child(1) .badge'),
            redis: document.querySelector('.card-body .mb-2:nth-child(2) .badge'),
            api: document.querySelector('.card-body .mb-2:nth-child(3) .badge')
        };

        Object.keys(statusElements).forEach(key => {
            const element = statusElements[key];
            if (element) {
                element.className = `badge ${status[key] ? 'bg-success' : 'bg-danger'}`;
                element.textContent = status[key] ? '正常' : '异常';
            }
        });
    }
}

// 工具函数
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function getStatusBadgeClass(status) {
    const classes = {
        'active': 'bg-success',
        'inactive': 'bg-warning',
        'banned': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '正常',
        'inactive': '未激活',
        'banned': '已封禁'
    };
    return texts[status] || status;
}

function getTierBadgeClass(tier) {
    const classes = {
        '985': 'bg-danger',
        '211': 'bg-warning',
        'tier1': 'bg-info',
        'tier2': 'bg-secondary'
    };
    return classes[tier] || 'bg-secondary';
}

function getPaymentStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'paid': 'bg-success',
        'failed': 'bg-danger',
        'refunded': 'bg-info'
    };
    return classes[status] || 'bg-secondary';
}

function getPaymentStatusText(status) {
    const texts = {
        'pending': '待支付',
        'paid': '已支付',
        'failed': '支付失败',
        'refunded': '已退款'
    };
    return texts[status] || status;
}

// 分页更新
function updatePagination(containerId, pagination, loadFunctionName) {
    const container = document.getElementById(containerId);
    if (!container || !pagination) return;

    const { current_page, total_pages } = pagination;
    let html = '';

    // 上一页
    html += `<li class="page-item ${current_page <= 1 ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page - 1}); return false;">上一页</a>
    </li>`;

    // 页码
    for (let i = Math.max(1, current_page - 2); i <= Math.min(total_pages, current_page + 2); i++) {
        html += `<li class="page-item ${i === current_page ? 'active' : ''}">
            <a class="page-link" href="#" onclick="${loadFunctionName}(${i}); return false;">${i}</a>
        </li>`;
    }

    // 下一页
    html += `<li class="page-item ${current_page >= total_pages ? 'disabled' : ''}">
        <a class="page-link" href="#" onclick="${loadFunctionName}(${current_page + 1}); return false;">下一页</a>
    </li>`;

    container.innerHTML = html;
}

// 查看详情函数（占位符）
function viewUser(id) {
    showAlert(`查看用户 ${id}`, 'info');
}

function editUser(id) {
    showAlert(`编辑用户 ${id}`, 'info');
}

function viewProfile(id) {
    showAlert(`查看档案 ${id}`, 'info');
}

function viewUniversity(id) {
    showAlert(`查看院校 ${id}`, 'info');
}

function viewRecommendation(id) {
    showAlert(`查看推荐 ${id}`, 'info');
}

function viewCareer(id) {
    showAlert(`查看职业 ${id}`, 'info');
}

function viewSimulation(id) {
    showAlert(`查看模拟 ${id}`, 'info');
}

function viewOrder(id) {
    showAlert(`查看订单 ${id}`, 'info');
}

function showAddUserModal() {
    showAlert('添加用户功能开发中', 'info');
}

function saveData() {
    showAlert('保存功能开发中', 'info');
}

// 处理系统配置保存
async function handleSystemConfigSave(e) {
    e.preventDefault();

    const currentYear = document.getElementById('currentYear').value;
    const detailedAnalysisPrice = document.getElementById('detailedAnalysisPrice').value;
    const simulationReportPrice = document.getElementById('simulationReportPrice').value;

    const configData = {
        current_year: parseInt(currentYear),
        pricing: {
            detailed_analysis: parseFloat(detailedAnalysisPrice),
            simulation_report: parseFloat(simulationReportPrice),
            premium_package: 499
        }
    };

    try {
        const data = await apiRequest('/admin/config', {
            method: 'PUT',
            body: JSON.stringify(configData)
        });

        if (data && data.code === 200) {
            showAlert('系统配置保存成功', 'success');
        } else {
            showAlert(data?.message || '保存失败', 'danger');
        }
    } catch (error) {
        console.error('保存配置错误:', error);
        showAlert('保存失败，请重试', 'danger');
    }
}

// 添加一些测试数据的功能
async function addTestData() {
    try {
        // 这里可以添加一些测试数据
        showAlert('测试数据添加功能开发中', 'info');
    } catch (error) {
        console.error('添加测试数据错误:', error);
        showAlert('添加测试数据失败', 'danger');
    }
}

// 导出数据功能
function exportData(type) {
    showAlert(`导出${type}数据功能开发中`, 'info');
}

// 刷新当前页面数据
function refreshCurrentData() {
    switch(currentSection) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'users':
            loadUsers();
            break;
        case 'profiles':
            loadProfiles();
            break;
        case 'universities':
            loadUniversities();
            break;
        case 'recommendations':
            loadRecommendations();
            break;
        case 'careers':
            loadCareers();
            break;
        case 'simulations':
            loadSimulations();
            break;
        case 'orders':
            loadOrders();
            break;
        case 'system':
            loadSystemConfig();
            break;
    }
    showAlert('数据已刷新', 'success');
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('页面错误:', e.error);
    showAlert('页面发生错误，请刷新重试', 'danger');
});

// 添加键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl+R 刷新数据
    if (e.ctrlKey && e.key === 'r') {
        e.preventDefault();
        refreshCurrentData();
    }

    // ESC 关闭模态框
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }
});