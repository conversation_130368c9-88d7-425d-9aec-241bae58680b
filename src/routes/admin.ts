import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { getConnection } from '../config/database';
import { adminAuthenticate, generateToken } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// 管理员登录
router.post('/login',
  [
    body('username').notEmpty().withMessage('用户名不能为空'),
    body('password').notEmpty().withMessage('密码不能为空')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;
    const connection = getConnection();

    // 查询管理员
    const [admins] = await connection.execute(
      'SELECT * FROM admins WHERE username = ?',
      [username]
    );

    const adminList = admins as any[];
    if (adminList.length === 0) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    const admin = adminList[0];

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    // 更新最后登录时间
    await connection.execute(
      'UPDATE admins SET last_login_time = CURRENT_TIMESTAMP WHERE id = ?',
      [admin.id]
    );

    // 生成JWT Token
    const accessToken = generateToken({ userId: admin.id.toString(), type: 'admin' });

    logger.info('管理员登录成功', { username, ip: req.ip });

    res.json({
      code: 200,
      message: '登录成功',
      data: {
        access_token: accessToken,
        expires_in: 7200,
        admin: {
          id: admin.id,
          username: admin.username,
          role: admin.role
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取控制台数据
router.get('/dashboard',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    const connection = getConnection();

    // 统计数据
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [profileCount] = await connection.execute('SELECT COUNT(*) as count FROM student_profiles');
    const [recommendationCount] = await connection.execute('SELECT COUNT(*) as count FROM recommendations');
    const [orderCount] = await connection.execute('SELECT COUNT(*) as count FROM orders');

    // 最近活动（示例数据）
    const [recentUsers] = await connection.execute(
      'SELECT phone, register_time FROM users ORDER BY register_time DESC LIMIT 5'
    );

    const activities = (recentUsers as any[]).map(user => ({
      action: '新用户注册',
      description: `用户 ${user.phone} 注册`,
      created_at: user.register_time
    }));

    res.json({
      code: 200,
      data: {
        stats: {
          users: (userCount as any[])[0].count,
          profiles: (profileCount as any[])[0].count,
          recommendations: (recommendationCount as any[])[0].count,
          orders: (orderCount as any[])[0].count
        },
        activities
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取用户列表
router.get('/users',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM users');
    const total = (countResult as any[])[0].total;

    // 获取用户列表
    const [users] = await connection.execute(
      `SELECT * FROM users ORDER BY register_time DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        users,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 添加用户
router.post('/users',
  adminAuthenticate,
  [
    body('phone').isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
    body('nickname').optional().isLength({ max: 50 }).withMessage('昵称长度不能超过50字符'),
    body('status').optional().isIn(['active', 'inactive', 'banned']).withMessage('状态值不正确')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const { phone, nickname, status = 'active' } = req.body;
    const userId = uuidv4();
    const connection = getConnection();

    try {
      // 检查手机号是否已存在
      const [existingUsers] = await connection.execute(
        'SELECT id FROM users WHERE phone = ?',
        [phone]
      );

      if ((existingUsers as any[]).length > 0) {
        return res.status(409).json({
          code: 409,
          message: '手机号已存在',
          timestamp: new Date().toISOString()
        });
      }

      // 插入新用户
      await connection.execute(
        'INSERT INTO users (id, phone, nickname, status, register_time) VALUES (?, ?, ?, ?, NOW())',
        [userId, phone, nickname, status]
      );

      logger.info('管理员添加用户', { userId, phone, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '用户添加成功',
        data: { userId },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('添加用户失败:', error);
      res.status(500).json({
        code: 500,
        message: '添加用户失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 更新用户
router.put('/users/:id',
  adminAuthenticate,
  [
    body('phone').optional().isMobilePhone('zh-CN').withMessage('手机号格式不正确'),
    body('nickname').optional().isLength({ max: 50 }).withMessage('昵称长度不能超过50字符'),
    body('status').optional().isIn(['active', 'inactive', 'banned']).withMessage('状态值不正确')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const userId = req.params.id;
    const { phone, nickname, status } = req.body;
    const connection = getConnection();

    try {
      // 检查用户是否存在
      const [existingUsers] = await connection.execute(
        'SELECT id FROM users WHERE id = ?',
        [userId]
      );

      if ((existingUsers as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '用户不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (phone) {
        updateFields.push('phone = ?');
        updateValues.push(phone);
      }
      if (nickname !== undefined) {
        updateFields.push('nickname = ?');
        updateValues.push(nickname);
      }
      if (status) {
        updateFields.push('status = ?');
        updateValues.push(status);
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '没有要更新的字段',
          timestamp: new Date().toISOString()
        });
      }

      updateFields.push('updated_at = NOW()');
      updateValues.push(userId);

      await connection.execute(
        `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      logger.info('管理员更新用户', { userId, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '用户更新成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('更新用户失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新用户失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 删除用户
router.delete('/users/:id',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const userId = req.params.id;
    const connection = getConnection();

    try {
      // 检查用户是否存在
      const [existingUsers] = await connection.execute(
        'SELECT id FROM users WHERE id = ?',
        [userId]
      );

      if ((existingUsers as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '用户不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 删除用户（级联删除会自动删除相关数据）
      await connection.execute('DELETE FROM users WHERE id = ?', [userId]);

      logger.info('管理员删除用户', { userId, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '用户删除成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('删除用户失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除用户失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 获取单个用户详情
router.get('/users/:id',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const userId = req.params.id;
    const connection = getConnection();

    try {
      const [users] = await connection.execute(
        'SELECT * FROM users WHERE id = ?',
        [userId]
      );

      if ((users as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '用户不存在',
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        code: 200,
        data: (users as any[])[0],
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('获取用户详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取用户详情失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 获取考生档案列表
router.get('/profiles',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM student_profiles');
    const total = (countResult as any[])[0].total;

    // 获取档案列表
    const [profiles] = await connection.execute(
      `SELECT * FROM student_profiles ORDER BY created_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        profiles,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 添加考生档案
router.post('/profiles',
  adminAuthenticate,
  [
    body('user_id').notEmpty().withMessage('用户ID不能为空'),
    body('name').notEmpty().withMessage('姓名不能为空').isLength({ max: 50 }).withMessage('姓名长度不能超过50字符'),
    body('score').isInt({ min: 0, max: 750 }).withMessage('分数必须在0-750之间'),
    body('province').notEmpty().withMessage('省份不能为空'),
    body('subject_combination').notEmpty().withMessage('科目组合不能为空'),
    body('exam_type').isIn(['new_gaokao', 'traditional']).withMessage('考试类型不正确')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const {
      user_id,
      name,
      score,
      province,
      subject_combination,
      exam_type,
      preferred_regions,
      major_preferences,
      family_economic_status,
      special_requirements
    } = req.body;

    const profileId = uuidv4();
    const connection = getConnection();

    try {
      // 检查用户是否存在
      const [existingUsers] = await connection.execute(
        'SELECT id FROM users WHERE id = ?',
        [user_id]
      );

      if ((existingUsers as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '用户不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 插入考生档案
      await connection.execute(
        `INSERT INTO student_profiles (
          id, user_id, name, score, province, subject_combination, exam_type,
          preferred_regions, major_preferences, family_economic_status, special_requirements
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          profileId, user_id, name, score, province, subject_combination, exam_type,
          JSON.stringify(preferred_regions || []),
          JSON.stringify(major_preferences || []),
          family_economic_status,
          JSON.stringify(special_requirements || {})
        ]
      );

      logger.info('管理员添加考生档案', { profileId, user_id, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '考生档案添加成功',
        data: { profileId },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('添加考生档案失败:', error);
      res.status(500).json({
        code: 500,
        message: '添加考生档案失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 更新考生档案
router.put('/profiles/:id',
  adminAuthenticate,
  [
    body('name').optional().isLength({ max: 50 }).withMessage('姓名长度不能超过50字符'),
    body('score').optional().isInt({ min: 0, max: 750 }).withMessage('分数必须在0-750之间'),
    body('province').optional().notEmpty().withMessage('省份不能为空'),
    body('subject_combination').optional().notEmpty().withMessage('科目组合不能为空'),
    body('exam_type').optional().isIn(['new_gaokao', 'traditional']).withMessage('考试类型不正确')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const profileId = req.params.id;
    const {
      name,
      score,
      province,
      subject_combination,
      exam_type,
      preferred_regions,
      major_preferences,
      family_economic_status,
      special_requirements
    } = req.body;

    const connection = getConnection();

    try {
      // 检查档案是否存在
      const [existingProfiles] = await connection.execute(
        'SELECT id FROM student_profiles WHERE id = ?',
        [profileId]
      );

      if ((existingProfiles as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '考生档案不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 构建更新字段
      const updateFields = [];
      const updateValues = [];

      if (name) {
        updateFields.push('name = ?');
        updateValues.push(name);
      }
      if (score !== undefined) {
        updateFields.push('score = ?');
        updateValues.push(score);
      }
      if (province) {
        updateFields.push('province = ?');
        updateValues.push(province);
      }
      if (subject_combination) {
        updateFields.push('subject_combination = ?');
        updateValues.push(subject_combination);
      }
      if (exam_type) {
        updateFields.push('exam_type = ?');
        updateValues.push(exam_type);
      }
      if (preferred_regions) {
        updateFields.push('preferred_regions = ?');
        updateValues.push(JSON.stringify(preferred_regions));
      }
      if (major_preferences) {
        updateFields.push('major_preferences = ?');
        updateValues.push(JSON.stringify(major_preferences));
      }
      if (family_economic_status) {
        updateFields.push('family_economic_status = ?');
        updateValues.push(family_economic_status);
      }
      if (special_requirements) {
        updateFields.push('special_requirements = ?');
        updateValues.push(JSON.stringify(special_requirements));
      }

      if (updateFields.length === 0) {
        return res.status(400).json({
          code: 400,
          message: '没有要更新的字段',
          timestamp: new Date().toISOString()
        });
      }

      updateFields.push('updated_at = NOW()');
      updateValues.push(profileId);

      await connection.execute(
        `UPDATE student_profiles SET ${updateFields.join(', ')} WHERE id = ?`,
        updateValues
      );

      logger.info('管理员更新考生档案', { profileId, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '考生档案更新成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('更新考生档案失败:', error);
      res.status(500).json({
        code: 500,
        message: '更新考生档案失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 删除考生档案
router.delete('/profiles/:id',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const profileId = req.params.id;
    const connection = getConnection();

    try {
      // 检查档案是否存在
      const [existingProfiles] = await connection.execute(
        'SELECT id FROM student_profiles WHERE id = ?',
        [profileId]
      );

      if ((existingProfiles as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '考生档案不存在',
          timestamp: new Date().toISOString()
        });
      }

      // 删除档案
      await connection.execute('DELETE FROM student_profiles WHERE id = ?', [profileId]);

      logger.info('管理员删除考生档案', { profileId, adminId: req.user.userId });

      res.json({
        code: 200,
        message: '考生档案删除成功',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('删除考生档案失败:', error);
      res.status(500).json({
        code: 500,
        message: '删除考生档案失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 获取单个考生档案详情
router.get('/profiles/:id',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const profileId = req.params.id;
    const connection = getConnection();

    try {
      const [profiles] = await connection.execute(
        'SELECT * FROM student_profiles WHERE id = ?',
        [profileId]
      );

      if ((profiles as any[]).length === 0) {
        return res.status(404).json({
          code: 404,
          message: '考生档案不存在',
          timestamp: new Date().toISOString()
        });
      }

      const profile = (profiles as any[])[0];

      // 解析JSON字段
      if (profile.preferred_regions) {
        profile.preferred_regions = JSON.parse(profile.preferred_regions);
      }
      if (profile.major_preferences) {
        profile.major_preferences = JSON.parse(profile.major_preferences);
      }
      if (profile.special_requirements) {
        profile.special_requirements = JSON.parse(profile.special_requirements);
      }

      res.json({
        code: 200,
        data: profile,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      logger.error('获取考生档案详情失败:', error);
      res.status(500).json({
        code: 500,
        message: '获取考生档案详情失败',
        timestamp: new Date().toISOString()
      });
    }
  })
);

// 获取院校专业列表
router.get('/universities',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM universities_majors');
    const total = (countResult as any[])[0].total;

    // 获取院校专业列表
    const [universities] = await connection.execute(
      `SELECT * FROM universities_majors ORDER BY created_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        universities,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取推荐记录列表
router.get('/recommendations',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM recommendations');
    const total = (countResult as any[])[0].total;

    // 获取推荐记录列表
    const [recommendations] = await connection.execute(
      `SELECT * FROM recommendations ORDER BY generated_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        recommendations,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取职业数据列表
router.get('/careers',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM careers');
    const total = (countResult as any[])[0].total;

    // 获取职业数据列表
    const [careers] = await connection.execute(
      `SELECT * FROM careers ORDER BY created_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        careers,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取模拟记录列表
router.get('/simulations',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM simulations');
    const total = (countResult as any[])[0].total;

    // 获取模拟记录列表
    const [simulations] = await connection.execute(
      `SELECT * FROM simulations ORDER BY generated_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        simulations,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取订单列表
router.get('/orders',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM orders');
    const total = (countResult as any[])[0].total;

    // 获取订单列表
    const [orders] = await connection.execute(
      `SELECT * FROM orders ORDER BY created_at DESC LIMIT ${size} OFFSET ${offset}`
    );

    res.json({
      code: 200,
      data: {
        orders,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取系统配置
router.get('/config',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    res.json({
      code: 200,
      data: {
        current_year: 2025,
        pricing: {
          detailed_analysis: 19.9,
          simulation_report: 199,
          premium_package: 499
        },
        features: {
          ai_simulation_enabled: true,
          career_planning_enabled: true,
          voice_consultation_enabled: false
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 保存系统配置
router.put('/config',
  adminAuthenticate,
  [
    body('current_year').isInt({ min: 2020, max: 2030 }).withMessage('年份范围不正确'),
    body('pricing.detailed_analysis').isFloat({ min: 0 }).withMessage('价格必须为正数'),
    body('pricing.simulation_report').isFloat({ min: 0 }).withMessage('价格必须为正数')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    // TODO: 保存系统配置到数据库或配置文件
    logger.info('系统配置已更新', { config: req.body, adminId: req.user.userId });

    res.json({
      code: 200,
      message: '系统配置保存成功',
      timestamp: new Date().toISOString()
    });
  })
);

// 检查系统状态
router.get('/status',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    try {
      const connection = getConnection();
      
      // 检查数据库连接
      await connection.ping();
      
      // TODO: 检查Redis连接
      // TODO: 检查其他服务状态
      
      res.json({
        code: 200,
        data: {
          database: true,
          redis: true,
          api: true
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.json({
        code: 200,
        data: {
          database: false,
          redis: false,
          api: true
        },
        timestamp: new Date().toISOString()
      });
    }
  })
);

export default router; 