import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import { getConnection } from '../config/database';
import { adminAuthenticate, generateToken } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// 管理员登录
router.post('/login',
  [
    body('username').notEmpty().withMessage('用户名不能为空'),
    body('password').notEmpty().withMessage('密码不能为空')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    const { username, password } = req.body;
    const connection = getConnection();

    // 查询管理员
    const [admins] = await connection.execute(
      'SELECT * FROM admins WHERE username = ?',
      [username]
    );

    const adminList = admins as any[];
    if (adminList.length === 0) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    const admin = adminList[0];

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, admin.password);
    if (!isValidPassword) {
      return res.status(401).json({
        code: 401,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
    }

    // 更新最后登录时间
    await connection.execute(
      'UPDATE admins SET last_login_time = CURRENT_TIMESTAMP WHERE id = ?',
      [admin.id]
    );

    // 生成JWT Token
    const accessToken = generateToken({ userId: admin.id.toString(), type: 'admin' });

    logger.info('管理员登录成功', { username, ip: req.ip });

    res.json({
      code: 200,
      message: '登录成功',
      data: {
        access_token: accessToken,
        expires_in: 7200,
        admin: {
          id: admin.id,
          username: admin.username,
          role: admin.role
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取控制台数据
router.get('/dashboard',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    const connection = getConnection();

    // 统计数据
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [profileCount] = await connection.execute('SELECT COUNT(*) as count FROM student_profiles');
    const [recommendationCount] = await connection.execute('SELECT COUNT(*) as count FROM recommendations');
    const [orderCount] = await connection.execute('SELECT COUNT(*) as count FROM orders');

    // 最近活动（示例数据）
    const [recentUsers] = await connection.execute(
      'SELECT phone, register_time FROM users ORDER BY register_time DESC LIMIT 5'
    );

    const activities = (recentUsers as any[]).map(user => ({
      action: '新用户注册',
      description: `用户 ${user.phone} 注册`,
      created_at: user.register_time
    }));

    res.json({
      code: 200,
      data: {
        stats: {
          users: (userCount as any[])[0].count,
          profiles: (profileCount as any[])[0].count,
          recommendations: (recommendationCount as any[])[0].count,
          orders: (orderCount as any[])[0].count
        },
        activities
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取用户列表
router.get('/users',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM users');
    const total = (countResult as any[])[0].total;

    // 获取用户列表
    const [users] = await connection.execute(
      'SELECT * FROM users ORDER BY register_time DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        users,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取考生档案列表
router.get('/profiles',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM student_profiles');
    const total = (countResult as any[])[0].total;

    // 获取档案列表
    const [profiles] = await connection.execute(
      'SELECT * FROM student_profiles ORDER BY created_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        profiles,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取院校专业列表
router.get('/universities',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM universities_majors');
    const total = (countResult as any[])[0].total;

    // 获取院校专业列表
    const [universities] = await connection.execute(
      'SELECT * FROM universities_majors ORDER BY created_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        universities,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取推荐记录列表
router.get('/recommendations',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM recommendations');
    const total = (countResult as any[])[0].total;

    // 获取推荐记录列表
    const [recommendations] = await connection.execute(
      'SELECT * FROM recommendations ORDER BY generated_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        recommendations,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取职业数据列表
router.get('/careers',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM careers');
    const total = (countResult as any[])[0].total;

    // 获取职业数据列表
    const [careers] = await connection.execute(
      'SELECT * FROM careers ORDER BY created_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        careers,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取模拟记录列表
router.get('/simulations',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM simulations');
    const total = (countResult as any[])[0].total;

    // 获取模拟记录列表
    const [simulations] = await connection.execute(
      'SELECT * FROM simulations ORDER BY generated_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        simulations,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取订单列表
router.get('/orders',
  adminAuthenticate,
  asyncHandler(async (req: any, res: any) => {
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 20;
    const offset = (page - 1) * size;

    const connection = getConnection();

    // 获取总数
    const [countResult] = await connection.execute('SELECT COUNT(*) as total FROM orders');
    const total = (countResult as any[])[0].total;

    // 获取订单列表
    const [orders] = await connection.execute(
      'SELECT * FROM orders ORDER BY created_at DESC LIMIT ?, ?',
      [offset, size]
    );

    res.json({
      code: 200,
      data: {
        orders,
        pagination: {
          current_page: page,
          page_size: size,
          total_count: total,
          total_pages: Math.ceil(total / size)
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 获取系统配置
router.get('/config',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    res.json({
      code: 200,
      data: {
        current_year: 2025,
        pricing: {
          detailed_analysis: 19.9,
          simulation_report: 199,
          premium_package: 499
        },
        features: {
          ai_simulation_enabled: true,
          career_planning_enabled: true,
          voice_consultation_enabled: false
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

// 保存系统配置
router.put('/config',
  adminAuthenticate,
  [
    body('current_year').isInt({ min: 2020, max: 2030 }).withMessage('年份范围不正确'),
    body('pricing.detailed_analysis').isFloat({ min: 0 }).withMessage('价格必须为正数'),
    body('pricing.simulation_report').isFloat({ min: 0 }).withMessage('价格必须为正数')
  ],
  asyncHandler(async (req: any, res: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        code: 400,
        message: '参数验证失败',
        errors: errors.array()
      });
    }

    // TODO: 保存系统配置到数据库或配置文件
    logger.info('系统配置已更新', { config: req.body, adminId: req.user.userId });

    res.json({
      code: 200,
      message: '系统配置保存成功',
      timestamp: new Date().toISOString()
    });
  })
);

// 检查系统状态
router.get('/status',
  adminAuthenticate,
  asyncHandler(async (_req: any, res: any) => {
    try {
      const connection = getConnection();
      
      // 检查数据库连接
      await connection.ping();
      
      // TODO: 检查Redis连接
      // TODO: 检查其他服务状态
      
      res.json({
        code: 200,
        data: {
          database: true,
          redis: true,
          api: true
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.json({
        code: 200,
        data: {
          database: false,
          redis: false,
          api: true
        },
        timestamp: new Date().toISOString()
      });
    }
  })
);

export default router; 