import express from 'express';
import { asyncHandler } from '../middleware/errorHandler';

const router = express.Router();

// 获取应用配置
router.get('/app',
  asyncHandler(async (_req: any, res: any) => {
    res.json({
      code: 200,
      data: {
        version: '1.0.0',
        features: {
          ai_enabled: true,
          payment_enabled: true
        }
      },
      timestamp: new Date().toISOString()
    });
  })
);

export default router; 